{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"]}, "types": ["@cloudflare/workers-types/2023-07-01"], "noErrorTruncation": true}, "include": ["**/*.ts", "**/*.tsx", ".next/types/**/*.ts", ".vercel/output/static/types/**/*.ts", "next-env-custom.d.ts", "next-env.d.ts", "out/types/**/*.ts"], "exclude": ["node_modules"]}