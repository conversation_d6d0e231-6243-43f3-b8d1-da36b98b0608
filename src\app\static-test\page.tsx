import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Static Test - Free Games Online',
  description: 'Static test page for SEO optimization',
};

export default function StaticTestPage() {
  return (
    <main className="min-h-screen bg-gradient-to-b from-gray-100 to-gray-200">
      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="flex flex-wrap gap-6">
          
          {/* Left Sidebar */}
          <aside className="hidden lg:block w-36 flex-shrink-0">
            <div className="bg-white rounded-lg shadow-md p-4 mb-6">
              <h3 className="font-bold text-gray-800 mb-4 text-center">🔥 Hot Games</h3>
              <div className="space-y-3">
                <div className="text-center">
                  <img 
                    src="https://cdn.sprunki.com/img/incredibox-sprunki.webp" 
                    alt="Sprunki Retake"
                    className="w-20 h-20 rounded-full mx-auto mb-2"
                  />
                  <p className="text-xs font-medium text-gray-700">Sprunki Retake</p>
                </div>
                <div className="text-center">
                  <img 
                    src="https://image.free-gamesonline.com/sprunki-games-categories.jpg" 
                    alt="Sprunki Games"
                    className="w-20 h-20 rounded-lg mx-auto mb-2"
                  />
                  <p className="text-xs font-medium text-gray-700">Sprunki Games</p>
                </div>
              </div>
            </div>
          </aside>

          {/* Main Content */}
          <div className="flex-1 min-w-0">
            {/* Featured Game Container */}
            <div className="bg-white rounded-3xl shadow-lg overflow-hidden mb-6 relative">
              <div className="relative w-full h-[650px] bg-gradient-to-br from-blue-500 via-purple-600 to-pink-500">
                {/* Game Thumbnail and Play Button */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center text-white">
                    <div className="mb-8">
                      <img
                        src="https://cdn.sprunki.com/img/incredibox-sprunki.webp"
                        alt="Sprunki Retake"
                        width="195"
                        height="195"
                        className="rounded-full mx-auto mb-6"
                      />
                    </div>
                    <div className="mb-8">
                      <h1 className="text-5xl font-bold mb-4">Sprunki Retake</h1>
                      <a
                        href="https://game.sprunki.com/sprunki-retake/index.html"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="bg-white text-green-600 px-8 py-4 rounded-full text-2xl font-bold hover:bg-gray-100 transition-colors uppercase shadow-lg inline-block"
                      >
                        Play Now
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              {/* Bottom Game Info Bar */}
              <div className="bg-white h-16 flex items-center justify-between px-6">
                <div className="flex items-center">
                  <img
                    src="https://cdn.sprunki.com/img/incredibox-sprunki.webp"
                    alt="Sprunki Retake"
                    width="40"
                    height="40"
                    className="rounded-full mr-3"
                  />
                  <div>
                    <h2 className="font-bold text-gray-800">Sprunki Retake</h2>
                    <p className="text-sm text-gray-600">Music & Rhythm Game</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2 text-gray-600">
                    <span className="text-2xl">👍</span>
                    <span className="font-medium">6.514K</span>
                  </div>
                  <a
                    href="https://game.sprunki.com/sprunki-retake/index.html"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-gray-800 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors flex items-center space-x-2"
                  >
                    <span>⛶</span>
                    <span className="hidden sm:inline">Play Fullscreen</span>
                  </a>
                </div>
              </div>
            </div>

            {/* Game Information */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
              <h2 className="text-2xl font-bold text-gray-800 mb-4">About Sprunki Retake</h2>
              <p className="text-gray-600 leading-relaxed mb-4">
                Sprunki Retake is an innovative music-mixing game that takes the beloved Incredibox-inspired gameplay to new heights. 
                This mod introduces expressive hand animations for each character, adding a vibrant layer of personality and interactivity.
              </p>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className="text-2xl mb-2">🎵</div>
                  <div className="text-sm font-medium text-gray-700">Music Creation</div>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className="text-2xl mb-2">🎮</div>
                  <div className="text-sm font-medium text-gray-700">Interactive</div>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className="text-2xl mb-2">🆓</div>
                  <div className="text-sm font-medium text-gray-700">Free to Play</div>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className="text-2xl mb-2">📱</div>
                  <div className="text-sm font-medium text-gray-700">Mobile Friendly</div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Sidebar */}
          <aside className="w-full lg:w-80 flex-shrink-0">
            {/* More Games */}
            <div className="bg-white rounded-lg shadow-md p-4 mb-6">
              <h3 className="font-bold text-gray-800 mb-4">🎮 More Games</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-lg transition-colors">
                  <img 
                    src="https://image.free-gamesonline.com/puzzle-games-categories.jpg" 
                    alt="Puzzle Games"
                    className="w-12 h-12 rounded-lg"
                  />
                  <div>
                    <p className="font-medium text-gray-800">Puzzle Games</p>
                    <p className="text-xs text-gray-600">Brain Training</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-lg transition-colors">
                  <img 
                    src="https://image.free-gamesonline.com/racing-games-categorie.jpg" 
                    alt="Racing Games"
                    className="w-12 h-12 rounded-lg"
                  />
                  <div>
                    <p className="font-medium text-gray-800">Racing Games</p>
                    <p className="text-xs text-gray-600">Speed & Action</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Ad Space */}
            <div className="bg-gray-100 rounded-lg p-4 text-center">
              <p className="text-gray-500 text-sm">Advertisement</p>
              <div className="w-full h-64 bg-gray-200 rounded-lg mt-2 flex items-center justify-center">
                <span className="text-gray-400">300x250 Ad</span>
              </div>
            </div>
          </aside>

        </div>
      </div>
    </main>
  );
}
