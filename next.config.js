/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export',
  distDir: 'out',
  images: {
    unoptimized: true,
  },
  // 确保 Cloudflare Pages 可以处理静态导出
  trailingSlash: true,
  // 禁用严格模式以避免一些问题
  reactStrictMode: false,
  // 优化 SEO 和减少客户端 JavaScript
  experimental: {
    // 减少客户端 JavaScript 包大小
    optimizePackageImports: ['react', 'react-dom'],
  },
  // 减少运行时 JavaScript
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  // 优化静态导出
  generateEtags: false,
}

module.exports = nextConfig 