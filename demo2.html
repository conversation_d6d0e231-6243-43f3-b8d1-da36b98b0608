
<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charSet="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#e5e5e5">
    <title>Sprunki Retake  - Free Play Incredibox Mod Online</title>
    <meta name="title" content="Sprunki Retake  - Free Play Incredibox Mod Online">
    <meta name="description" content="Unleash your creativity with Sprunki Retake! Enjoy expressive hand animations and engaging music mixing in this free, no-download game. Create your unique tracks!" />
    <link rel="canonical" href="https://sprunki.com/sprunki-retake" />
    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "WebSite",
            "name": "Sprunki Retake  - Free Play Incredibox Mod Online",
            "alternateName": "Sprunki",
            "url": "https://sprunki.com/sprunki-retake",
            "description": "Unleash your creativity with Sprunki Retake! Enjoy expressive hand animations and engaging music mixing in this free, no-download game. Create your unique tracks!"
        }
    </script>
    <meta property="og:title" content="Sprunki Retake  - Free Play Incredibox Mod Online" itemprop="headline" />
    <meta property="og:type" content="website" />
    <meta property="og:url" itemprop="url" content="https://sprunki.com/sprunki-retake" />
    <meta property="og:image" itemprop="thumbnailUrl" content="https://cdn.sprunki.com/img/incredibox-sprunki.webp" />
    <meta property="og:description" content="Unleash your creativity with Sprunki Retake! Enjoy expressive hand animations and engaging music mixing in this free, no-download game. Create your unique tracks!" itemprop="description" />
    <meta property="og:site_name" content="Sprunkin" />
    <meta name="twitter:title" content="Sprunki Retake  - Free Play Incredibox Mod Online" />
    <meta name="twitter:url" content="https://sprunki.com/sprunki-retake" />
    <meta name="twitter:image" content="https://cdn.sprunki.com/img/incredibox-sprunki.webp" />
    <meta name="twitter:description" content="Unleash your creativity with Sprunki Retake! Enjoy expressive hand animations and engaging music mixing in this free, no-download game. Create your unique tracks!">
    <meta name="twitter:card" content="summary" />
    <script src="https://cdn.sprunki.com/static/mb9/js/jquery.min.js" type="f92e0af06a98d6925affa0f3-text/javascript"></script>
    <link rel="icon" href="https://cdn.sprunki.com/logo/sprunki-favicon-m64x64.webp"/>
<link rel="apple-touch-icon" href="https://cdn.sprunki.com/logo/sprunki-favicon-m64x64.webp"/>
<link rel="apple-touch-icon" sizes="57x57" href="https://cdn.sprunki.com/logo/sprunki-favicon-m64x64.webp">
<link rel="apple-touch-icon" sizes="72x72" href="https://cdn.sprunki.com/logo/sprunki-favicon-m64x64.webp">
<link rel="apple-touch-icon" sizes="114x114" href="https://cdn.sprunki.com/logo/sprunki-favicon-m114x114.webp">
<link rel="apple-touch-icon" sizes="120x120" href="https://cdn.sprunki.com/logo/sprunki-favicon-m120x120.webp">
<link rel="apple-touch-icon" sizes="144x144" href="https://cdn.sprunki.com/logo/sprunki-favicon-m144x144.webp">
<link rel="apple-touch-icon" sizes="228x228" href="https://cdn.sprunki.com/logo/sprunki-favicon-m228x228.webp">
    <script src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2680823943926888" crossorigin="anonymous" type="f92e0af06a98d6925affa0f3-text/javascript"></script>
<script src="https://analytics.ahrefs.com/analytics.js" data-key="hX7TP7RQq71DMoA8BBLALQ" type="f92e0af06a98d6925affa0f3-text/javascript"></script>
     	<!-- Google tag (gtag.js) -->
<script src="https://www.googletagmanager.com/gtag/js?id=G-B6W1P289Z4" type="f92e0af06a98d6925affa0f3-text/javascript"></script>
<script type="f92e0af06a98d6925affa0f3-text/javascript">
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-B6W1P289Z4');
</script>
<link rel="preconnect" href="https://cdn.sprunki.com">
<link rel="preconnect" href="https://game.sprunki.com">
<link rel="dns-prefetch" href="https://static.addtoany.com">
<link rel="preconnect" href="https://user.sprunki.com">
    <style>

    /* Main Content Styles */
    .content-area-1 {
        max-width: 1598px;
        width: 100%;
        margin: 0 auto;
        padding: 20px 29px;
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
    }

    .left-sidebar {

        width: 149px;
    }

    .main-content {
        flex: 6;
        min-width: 300px;
        max-width: 990px;
    }

    .right-sidebar {
        flex: 3.3;
        width: 34%;
        min-width: 270px;
    }

    .ad-container {
        /*background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));*/
        /*color: white;*/
        /*padding: 15px;*/
        border-radius: 8px;
        margin-bottom: 20px;
        text-align: center;
        min-height: 100px;
        /*display: flex;*/
        align-items: center;
        /*justify-content: center;*/
    }

    .game-card {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s;
        margin-bottom: 20px;
    }

    .game-card:hover {
        transform: translateY(-5px);
    }

    .game-card img {
        width: 100%;
        height: 94px;
        object-fit: cover;
    }

    .game-card h3 {
        font-size: 14px;
        margin-bottom: 5px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: center;
    }

    .game-iframe-container {
        width: 100%;
        /*height: 79vh;*/
        background: white;
        border-radius: 48px;
        overflow: hidden;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }

    .game-iframe-container iframe {
        width: 100%;
        height: 100%;
        border: none;
    }

    .comments-section {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }

    .game-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(149px, 1fr));
        gap: 12px;
        margin-bottom: 15px;
        max-height: 1410px;
        overflow:hidden;
    }

    .load-more {
        margin: 30px auto 0 ;
        display: block;
        width: 100%;
        max-width:160px;
        padding: 10px;
        background: var(--primary-color);
        color: white;
        border: none;
        border-radius: 5px;
        font-weight: 600;
        cursor: pointer;
        max-height:42px;
        transition: background 0.3s;
        text-align: center;
    }

    .load-more:hover {
        background: var(--secondary-color);
    }

    /* Content Area 2 */
    .content-area-2 {
        width: 100%;
        
        max-width: 1598px;
        margin: 0 auto;
        padding: 40px 29px;
        background: white;
    }

    .about-section {
        margin: 0 auto;
        text-align: center;
    }

    .about-section h2 {
        font-size: 32px;
        margin-bottom: 20px;
        color: var(--primary-color);
    }

    /* Footer Styles */
    footer {
        background: var(--dark-color);
        color: white;
        padding: 30px 29px;
    }

    .footer-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
    }

    .footer-links {
        display: flex;
        gap: 20px;
    }

    .footer-links a {
        transition: color 0.3s;
    }

    .footer-links a:hover {
        color: var(--secondary-color);
    }

    .language-selector {
        margin-top: 20px;
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        justify-content: center;
    }

    .language-card {
        background: rgba(255, 255, 255, 0.1);
        padding: 5px 10px;
        border-radius: 20px;
        transition: background 0.3s;
        cursor: pointer;
    }

    .language-card:hover {
        background: rgba(255, 255, 255, 0.2);
    }

    /* Responsive Styles */
    @media (max-width: 1360px) {
        .left-sidebar {
            display: none;
        }
        header,.games-container {
            padding: 10px 20px;
        }
        .content-area-2 {
            padding: 40px 20px;
        }
        .content-area-1 {
            padding: 40px 20px;
        }
        .main-content {
            flex: 8;
        }

        .right-sidebar {
            flex: 4;
        }
    }

    @media (max-width: 1024px) {
        header {
            flex-direction: column;
            padding: 10px;
        }

        .logo {
            margin-bottom: 15px;
            margin-left:;
        }

        nav ul {
            flex-wrap: wrap;
            justify-content: center;
        }

        nav ul li {
            margin: 0 10px 10px;
        }
        
        header,.games-container {
            padding: 20px 15px;
        }
        
        .content-area-2 {
            padding: 20px 15px;
        }
        
        .content-area-1 {
            padding: 20px 15px;
        }
        
        .search-box {
            margin: 15px 0 0;
            width: 100%;
        }

        .search-box input {
            width: 90%;
        }

        .right-sidebar {
            flex: 100%;
            margin-top: 20px;
        }

        .game-grid {
            grid-template-columns: repeat(auto-fill, minmax(120px, 2fr));
        }

        .footer-top {
            flex-direction: column;
            text-align: center;
        }

        .footer-links {
            margin-top: 20px;
            flex-wrap: wrap;
            justify-content: center;
        }
    }

    @media (max-width: 480px) {
        nav ul li {
            margin: 0 5px 10px;
            font-size: 14px;
        }

        .game-card img {
            height: 100px;
        }
        
        header,.games-container {
            padding: 10px 10px;
        }
        
        .content-area-2 {
            padding: 10px 10px;
        }
        
        .content-area-1 {
            padding: 10px 10px;
        }
        .game-iframe-container {
            /*height: 50vh;*/
        }
    }
    /* 中部3 */
    .about-section {
        display: flex;
        margin: 30px 0;
    }

    .about-left, .about-right {
        width: 50%;
        padding: 0 15px;
    }

    .about-left {
        background-color: white;
        padding: 20px;
        border-radius: 8px;
    }

    .about-right {
        background-color: white;
        padding: 20px;
        border-radius: 8px;
    }

    .rating-stars {
        color: #ffc107;
        margin-bottom: 10px;
    }

    .rating-bar {
        display: flex;
        align-items: center;
        margin-bottom: 5px;
    }

    .rating-label {
        width: 80px;
    }

    .rating-progress {
        flex-grow: 1;
        height: 10px;
        background-color: #e9e9e9;
        border-radius: 5px;
        margin: 0 10px;
        overflow: hidden;
    }

    .rating-progress-fill {
        height: 100%;
        background-color: #ffc107;
    }

    .rating-count {
        width: 30px;
        text-align: right;
    }

    .comments-section {
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid #eee;
    }
    .footer-links {
        width: 100%;
        display: flex;
        justify-content: center;
        margin-left: -16%;
    }

    .footer-link {
        margin: 0 15px;
        color: #aaa;
        font-size: 14px;
    }

    .footer-link:hover {
        color: white;
    }
    @media (max-width: 768px) {
        .about-section {
            flex-direction: column;
        }

        .about-left,
        .about-right {
            width: 100%;
            margin-bottom: 20px;
        }

        /* 确保 about-left 在 about-right 上面 */
        .about-left {
            order: 2;
        }

        .about-right {
            order: 1;
        }
    }
    :root {
        --primary-color: #6c5ce7;
        --secondary-color: #00cec9;
        --accent-color: #fd79a8;
        --dark-color: #2d3436;
        --light-color: #f5f6fa;
        --text-color: #2d3436;
        --text-light: #636e72;
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    body {
        background-color: var(--light-color);
        color: var(--text-color);
        line-height: 1.6;
    }

    a {
        text-decoration: none;
        color: inherit;
    }

    /* Header Styles */
    header {
        background-color: white;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 15px 29px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        top: 0;
        z-index: 100;
    }

    .logo {
        font-size: 28px;
        font-weight: 700;
        color: var(--primary-color);
        display: flex;
        align-items: center;
    }

    .logo span {
        color: var(--accent-color);
    }

    nav ul {
        display: flex;
        list-style: none;
        flex-wrap: wrap;
        justify-content: center;
    }

    nav ul li {
        margin-left: 25px;
    }

    nav ul li a {
        font-weight: 600;
        transition: color 0.3s;
        color:white;
    }

    nav ul li a:hover {
        color: white;
    }

    .search-box {
        margin: 5px;
        position: relative;
        margin-right: 20px;
        text-align: center;
    }

    .search-box button {
        transform: translateY(0%);
        background: none;
        border: none;
        margin-left: -30px;
        color: var(--text-light);
        cursor: pointer;
    }
    /* 全局样式 */
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    body {
        background-image: url(/themes/game9mb/public/image/body-bg.png);
        background-size: 100% 100%;
        background-color: #f5f5f5;
        color: #333;
        line-height: 1.6;
    }

    a {
        text-decoration: none;
        color: inherit;
    }

    .container {
        width: 100%;
        margin: 0 auto;
        padding: 0 15px;
    }

    /* 头部导航 */
    header {
        background-color: #1a1a2e;
        padding: 15px 0;
        /*position: sticky;*/
        top: 0;
        z-index: 100;
    }

    .header-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #242331;
    }

    .logo {
        font-size: 24px;
        font-weight: bold;
        color: white;
        margin-left: 70px;
    }

    .logo span {
        color: #4cc9f0;
    }

    .search-container {
        display: flex;
        align-items: center;
    }

    .search-box {
        margin:5px;
        position: relative;
        margin-right: 20px;
    }

    .search-box input {
        padding: 2px 15px;
        border-radius: 20px;
        border: 1px solid #ddd;
        min-width: 400px;
        width: 80%;
        height:40px;
        line-height: 40px;
        outline: none;
    }

    .search-icon {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #777;
    }

    .nav-links {
        display: flex;
    }

    header nav li {
        padding: 8px 15px;
        margin-left: 10px;
        border-radius: 5px;
        font-weight: bold;
        color: white;
        border-radius: 90px 90px 90px 90px;
        text-transform: uppercase;
        font-size: 14px;
        line-height:31px;
    }
    header nav li:nth-child(1) { background-color: #FFCA37; }
    header nav li:nth-child(2) { background-color: #9089F2; }
    header nav li:nth-child(3) { background-color: #A6D776; }
    header nav li:nth-child(4) { background-color: #FF9437; }
    header nav li:nth-child(5) { background-color: #C18BE5; }
    header nav li:nth-child(6) { background-color: #8E83D2; }
    header nav li:nth-child(7) { background-color: #25d366; }
    header nav li:nth-child(8) { background-color: #C1d366; }
    header nav li:nth-child(9) { background-color: #FF9366; }
    .section-title {
        display: flex;
        align-items: center;
        margin: 0px 0 15px;
        font-size: 18px;
        font-weight: bold;
    }

    .section-title i {
        margin-right: 10px;
        color: #4cc9f0;
    }
    .media-links {
        display: flex;
        flex-wrap: nowrap;
        gap: 0;
        margin-bottom: 20px;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        padding-bottom: 5px;
        margin-top: 29px;
    }
    .ad-banner {
        width: 100%;
        height: 300px;
        background-color: #e9e9e9;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        margin-bottom: 20px;
        color: #777;
        font-style: italic;
    }

    /* 中部2 */
    .categories {
        width: 100%;
        background-color: white;
        padding: 20px 0;
    }
    
    .category-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        justify-content: center; /* 水平居中 */
        align-items: center; /* 垂直居中 */
    }
    
    .category-item {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 12px;
        height: 89px;
        font-weight: bold;
        text-align: right;
    }
    
    .category-item i {
        margin-right: 0;
        margin-left: 30px;
        order: 1;
    }
    
    /* 分类项背景图片和颜色 */
    .category-item:nth-child(1) {
        background-image: url(/themes/game9mb/public/image/8.png);
        width: 264px;
        color: #5e548e;
    }
    .category-item:nth-child(2) {
        background-image: url(/themes/game9mb/public/image/7.png);
        width: 264px;
        color: #3a0ca3;
    }
    .category-item:nth-child(3) {
        background-image: url(/themes/game9mb/public/image/9.png);
        width: 264px;
        color: #240046;
    }
    .category-item:nth-child(4) {
        background-image: url(/themes/game9mb/public/image/1.png);
        width: 264px;
        color: #14213d;
    }
    .category-item:nth-child(5) {
        background-image: url(/themes/game9mb/public/image/2.png);
        width: 264px;
        color: #1b263b;
    }
    @media (max-width: 1586px) {
    /* 重新排列header布局 */
    header {
        flex-direction: column;
        padding: 15px 20px;
    }
    
    /* 将logo移动到顶部 */
    .logo {
        order: -1; /* 使logo成为第一个元素 */
        margin-bottom: 15px;
        margin-left:0;
        
    }
    
    /* 搜索框保持在中间 */
    .search-box {
        order: 0;
        margin: 0 auto 15px; /* 居中并添加底部间距 */
        width: 100%;
        max-width: 600px; /* 限制搜索框最大宽度 */
    }
    
    /* 导航菜单移动到搜索框下方 */
    nav {
        order: 1;
        width: 100%;
    }
    
    /* 导航菜单项居中显示 */
    nav ul {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    /* 调整导航菜单项间距 */
    nav ul li {
        margin: 5px 10px;
    }
}

/* 对于更小的屏幕进一步调整 */
@media (max-width: 768px) {
    .search-box input {
        min-width: unset; /* 移除最小宽度限制 */
        width: 90%;
    }
    
    nav ul li {
        font-size: 14px;
        padding: 5px 10px;
    }
}

</style>
</head>
<body>
<!-- Header Navigation -->
<header>
    <div class="logo"><a href="/"><img width="auto" height="37" src="/static/mb9/picture/logo.png" class="logo" alt="Sprunki"></a></div>
    <div class="search-box">
        <form action="/search.html" method="get">
            <input type="text" name="s" placeholder="Search...">
            <button>🔍</button>
        </form>
    </div>
    <nav>
        <ul>

        
            <li> <a href="/games" title="GAMES">GAMES</a></li>
            <li> <a href="https://sprunki.com/incredibox-sprunki" title="Incredibox">Incredibox</a></li>
            <li> <a href="/phase"  title="PHASE">PHASE</a></li>
            <li><a href="https://sprunki.com/sprunki-kiss" title="Kiss Mod">Kiss Mod</a></li>
            <li><a href="https://sprunki.com/sprunki-retake"  title="Retake">Retake</a></li>
            <li><a href="/pyramixed"  title="PYRAMIXED">PYRAMIXED</a></li>
            <li><a href="/video"  title="Videos">Videos</a></li>            


        </ul>
    </nav>
</header>





<!-- Main Content Area 1 -->
<div class="content-area-1">
    <!-- Left Sidebar -->
    <div class="left-sidebar">
        <!--<div class="ad-banner">-->
        <!--    Advertisement - 300x250-->
        <!--</div>-->
        <div class="ad-container" style="min-height: 600px;">
            <!--&lt;!&ndash; sprunki.com.ads.head2 &ndash;&gt;-->
            <ins class="adsbygoogle"
                 style="display:block"
                 data-ad-client="ca-pub-2680823943926888"
                 data-ad-slot="1120096231"
                 data-ad-format="auto"
                 data-full-width-responsive="true"></ins>
            <script type="f92e0af06a98d6925affa0f3-text/javascript">
                (adsbygoogle = window.adsbygoogle || []).push({});
            </script>
        </div>
        <div class="section-title">
            <i class="fas fa-gamepad"></i> Popular Games        </div>


                                    <a href="/sprunky" title="Sprunki">
                                        <div class="game-card">
                        <img src="https://cdn.sprunki.com/img/sprunky.webp" alt="Sprunki">
                        <h3>Sprunki</h3>
                    </div>
                </a>
        
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="game-iframe-container">
            <script src="/fullscreen.js" type="f92e0af06a98d6925affa0f3-text/javascript"></script>
            <style>

                .custom-game-container {
                    width: 100%;
                    max-width: 1200px;
                    height: 650px;
                    background: #fff;
                    /*border-radius: 15px;*/
                    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    position: relative;
                    overflow: hidden;
                    margin: 0px auto 0px auto;
                    background-image: url(/themes/game9mb/public/image/iframe.png);
                    background-size: cover;
                    background-position: center;
                    background-repeat: no-repeat;
                    /*padding-bottom: 64px;*/
                }
                .custom-thumbnail-container {
                    text-align: center;
                    position: absolute;
                    width: 94%;
                    margin: 5% auto;
                    height: 90%;
                    z-index: 2;
                    /*background-color: rgba(0, 0, 0, 0.5);*/
                    border-radius: 20px;
                }
                .custom-thumbnail {
                    width: 340px; /* 修改为400px */
                    height: 260px; /* 修改为250px */
                    background-size: cover;
                    border-radius: 10px;
                    margin: 0 auto 20px;
                    /*box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);*/
                }
                .custom-game-title h1 {
                    font-size: 46px;
                    font-weight: bold;
                    /*color: #6c63ff;*/
                    margin-bottom: 20px;
                }
                .custom-play-button {
                    padding: 10px 20px;
                    font-size: 1rem;
                    font-weight: bold;
                    color: green;
                    background: white;
                    border: none;
                    border-radius: 60PX;
                    cursor: pointer;
                    height: 80px;
                    font-size: 28px;
                    width: 250PX;
                    text-transform: uppercase;
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                    transition: background 0.3s ease;
                    margin-top: 25%;
                }

                .custom-iframe-container {
                    display: none;
                    width: 100%;
                    /*height: 100%;*/
                    position: absolute;
                    top: 0;
                    left: 0;
                    bottom: 0px;
                    right: 0px;
                }
                .custom-iframe-container iframe {
                    width: 100%;
                    height: 100%;
                    border: none;
                }
                #gamePlayerBg{
                    /*display: block;*/
                    /*width: 100%;*/
                    /*height: 100%;*/
                    position: absolute;
                    /*top: 0;*/
                    /*left: 0;*/
                    /*z-index: 1;*/
                }
                #gamePlayerBg .gamePlayerBgImage{
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    filter: blur(45px);
                    background-size: cover;
                    transform: scale(1.3);
                    background-image: url('https://cdn.sprunki.com/img/incredibox-sprunki.webp') !important;
                }
                .custom-thumbnail-container .leftTdk{
                    position: absolute;
                    left: 5%;
                    top: 20%;
                    width: 61%;
                    text-align: left;
                    color: #fff;
                }
                .custom-thumbnail-container .custom-thumbnail{
                    position: absolute;
                    right: 5%;
                    top: 30%;
                    width: 30%;
                }
                .custom-thumbnail-container .custom-thumbnail img{
                    width: 195px;
                    height: 195px;
                    border-radius: 50%;
                }
                .bottomTdkFullscreen{
                    position: relative;
                    /*bottom: 0;*/
                    /*left: 0;*/
                    /*right:0px;*/
                    width: 100%;
                    height: 64px;
                    background: white;
                    color: black;
                    /*z-index: 99999;*/
                }
                .bottomTdkFullscreen .bottomLeftTdk{
                    position: absolute;
                    top: 0px;
                    left: 0px;
                    width: 50%;
                }
                .bottomTdkFullscreen .bottomLeftTdk .imgdiv{
                    position: absolute;
                    left: 48px;
                    top: 5px;
                    width: 54px;
                    height: 54px;
                }
                .bottomTdkFullscreen .bottomLeftTdk .imgdiv img{
                    width: 100%;
                    height: 100%;
                }
                .bottomTdkFullscreen .bottomLeftTdk .tdkHtml{
                    position: absolute;
                    top: 18px;
                    left: 110px;
                }
                .bottomTdkFullscreen .bottomLeftTdk .tdkHtml{
                    font-size: 1rem;
                    text-transform: uppercase;
                    font-weight: 700;
                    color: black;
                    text-align: left;
                    margin-bottom: 0px;
                }
                .bottomTdkFullscreen .bottomLeftTdk .tdkHtml span{
                    font-size: 14px;
                    color: #e5d0d0;
                    text-align: left;
                    display: block;
                }
                .bottomTdkFullscreen .rightFullscreen{
                    position: absolute;
                    top: 0px;
                    right: 0px;
                    width: 50%;
                    text-align: right;
                }
                .bottomTdkFullscreen .rightFullscreen .bottomFull{
                    color: #fff;
                    line-height: 64px;
                    padding: 0px;
                    background-color: #111739;
                    border: 0px;
                    margin-right: 10px;
                    font-size: 1rem;
                    cursor: pointer !important;
                    padding-left: 45px;
                    background: url(/themes/game9mb/public/image/fullscreen.png) no-repeat left center;
                    background-size: 40px;
                }
                .bottomTdkFullscreen .rightFullscreen p{
                    display: inline-block;
                    margin-right: 30px;
                    padding-left: 35px;
                    line-height: 64px;
                    padding-left: 44px;
                    cursor: pointer !important;
                    background: url(/themes/game9mb/public/image/zan.png) no-repeat left center;
                    background-size: 40px;
                }

                #likeNum img {
                    position: absolute;
                    right: 210px;
                    top: 15px;
                }

                .mattop60{
                    margin-top: 60px;
                }
                .hidden,.mobile{
                    display: none;
                }
                .pcbut{
                    display: inline-block;
                }
                .fullscreen-btn {
                    margin-top: 10px;
                    padding: 10px 20px;
                    background-color: #347A36;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    cursor: pointer;
                    font-size: 16px;
                }
                #exit-fullscreen-btn {
                    position: absolute;
                    top: 20px;
                    right: 20px;
                    z-index: 99999;
                }
                @media (max-width: 768px) {
                    .custom-thumbnail-container .custom-thumbnail{
                        margin-top: 30px;
                        position: static;
                        width: 100%;
                        height: auto;
                    }
                    .custom-thumbnail-container .custom-thumbnail img{
                        width: 154px;
                        height: 154px;
                    }
                    .custom-thumbnail-container .leftTdk{
                        margin-top: 30%;
                        position: static;
                        width: 100%;
                        text-align: center;
                    }
                    .bottomTdkFullscreen{
                        height: 128px;
                    }
                    .bottomTdkFullscreen .bottomLeftTdk{
                        position: static;
                        width: 100%;
                        height: 64px;
                    }
                    .bottomTdkFullscreen .rightFullscreen{
                        position: static;
                        width: 100%;
                        height: 64px;
                        text-align: right;
                    }
                    .bottomTdkFullscreen .rightFullscreen p{
                        /*margin-left: 10px;*/
                    }
                    .pcbut{
                        display: none;
                    }
                    .mobile{
                        display: inline-block;
                    }
                }
                /* 添加加载动画样式 */
                .custom-loading-container {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    display: none; /* 默认隐藏 */
                    align-items: center;
                    justify-content: center;
                    background-color: rgba(255, 255, 255, 0.8);
                    z-index: 10;
                }

                .custom-loading-gif {
                    width: 100%; /* 增大尺寸 */
                    height: 100%;
                }
                .custom-play-button {
                    font-size: 24px;
                    display: inline-block;
                    transition: transform 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
                    cursor: pointer;
                }

                .custom-play-button:hover {
                    transform: translateY(-15px);
                    color: #ff5722; /* 可选：改变颜色 */
                }

            </style>
            <div id="play" class="custom-game-container">
                <!--背景图片-->
                <div id="gamePlayerBg">
                    <div class="gamePlayerBgImage">

                    </div>
                </div>
                <div class="custom-loading-container" id="customLoadingContainer">
                    <img src="/themes/game9mb/public/image/load.gif" class="custom-loading-gif" alt="Loading...">
                </div>

                <!-- 缩略图及游戏标题 -->
                <div class="custom-thumbnail-container">
                    <div class="leftTdk">
                        <div class="custom-game-title"><h1>Sprunki Retake</h1></div>
                        <button class="custom-play-button" onclick="if (!window.__cfRLUnblockHandlers) return false; startCustomGame()" data-cf-modified-f92e0af06a98d6925affa0f3-="">Play Now</button>
                        <div class="custom-game-desc" style="display: none;">Unleash your creativity with Sprunki Retake! Enjoy expressive hand animations and engaging music mixing in this free, no-download game. Create your unique tracks!</div>
                    </div>
                </div>
                <!-- 游戏 iframe -->
                <div class="custom-iframe-container" id="customGameIframeContainer">
                    <iframe id="customGameIframe" src="https://game.sprunki.com/sprunki-retake/index.html" allowfullscreen title="Sprunki Retake"></iframe>
                    <button id="exit-fullscreen-btn" class="absolute hidden fullscreen-btn mattop60">
                        &lt;
                    </button>
                    <div id="domain-watermark" class="absolute hidden">

                    </div>
                </div>

            </div>
            <div class="bottomTdkFullscreen">
                <div class="bottomLeftTdk">
                    <div class="imgdiv">
                        <img src="https://cdn.sprunki.com/img/sprunki-retake.webp" alt="Sprunki Retake">
                    </div>
                    <div class="tdkHtml">
                        
                            Sprunki Retake                        
                    </div>
                </div>
                <div class="rightFullscreen">
                    <p id="likeNum" onclick="if (!window.__cfRLUnblockHandlers) return false; startCustomGameLike()" data-cf-modified-f92e0af06a98d6925affa0f3-="">

                        6.514K</p>
                    <button id="fullscreen-btn" class="bottomFull pcbut">-</button>
                    <button class="bottomFull play-game mobile hidden">-</button>

                </div>
            </div>
            <script type="f92e0af06a98d6925affa0f3-text/javascript">
                function startCustomGame() {
                    // 显示加载动画
                    document.getElementById('customLoadingContainer').style.display = 'flex';

                    // 隐藏缩略图
                    document.querySelector('.custom-thumbnail-container').style.display = 'none';
                    document.getElementById('gamePlayerBg').style.display = 'none';

                    const iframeContainer = document.getElementById('customGameIframeContainer');
                    const gameIframe = document.getElementById('customGameIframe');

                    // 确保iframe容器显示
                    iframeContainer.style.display = 'block';

                    // 重置iframe src确保触发加载（如果已经加载过）
                    gameIframe.src = gameIframe.src;

                    // 监听iframe加载事件
                    gameIframe.onload = function() {
                        // 隐藏加载动画
                        // document.getElementById('customLoadingContainer').style.display = 'none';
                        setTimeout(function (){
                            document.getElementById('customLoadingContainer').style.display = 'none';
                        },3000)
                        // 全屏处理逻辑
                        if(document.querySelector('.bottomTdkFullscreen').style.display == 'none') {
                            const fullscreenBtn = document.getElementById('fullscreen-btn');
                            const exitFullscreenBtn = document.getElementById('exit-fullscreen-btn');
                            const domainWatermark = document.getElementById('domain-watermark');
                            enterFullscreen(iframeContainer, gameIframe, fullscreenBtn, exitFullscreenBtn, domainWatermark);
                        }

                        if (isMobile()) {
                            const playGameBtn = document.querySelector('.play-game');
                            enterFullscreen(iframeContainer, gameIframe, fullscreenBtn, exitFullscreenBtn, domainWatermark);
                            document.querySelector('.bottomTdkFullscreen').style.display = 'none';
                        }

                        // ajaxClick();
                    };

                    // 如果iframe已经加载完成（缓存情况）
                    if (gameIframe.contentDocument && gameIframe.contentDocument.readyState === 'complete') {
                        gameIframe.onload();
                    }
                }
                function ajaxClick(){
                    var clickList = document.querySelectorAll('span[data-num]');
                    var clickNum = Number(clickList[0].getAttribute('data-num'));
                    var p = document.getElementById('clickNum');
                    var content = p.innerHTML;
                    content = content.replace("played ","");
                    content = content.replace(" times","");
                    if(clickNum == content){
                        fetch("/clickArtGameVideo?id=2279", {
                            method: 'GET',
                        })
                            .then(response => response.json())
                            .then(data => {
                                if(data==1){
                                    var p = document.getElementById('clickNum');

                                    var content = p.innerHTML;
                                    content = content.replace("played ","");
                                    content = content.replace(" times","");
                                    content = Number(content) + 1;
                                    p.innerHTML = "played " + content + " times";
                                    document.getElementById('clickNum').style.display = 'block';
                                }

                            })
                            .catch(error => {
                                console.error('Fetch error:', error);
                            });
                    }
                }
                function startCustomGameLike() {
                    if(document.getElementById('likeNum').style.display==''){
                        fetch("/likeArtGameVideo?id=2279", {
                            method: 'GET',
                        })
                            .then(response => response.json())
                            .then(data => {
                                if(data==1){
                                    var p = document.getElementById('likeNum');

                                    var content = p.innerHTML;
                                    if(/K/.test(content)==true){
                                        content = Math.ceil(Number(content.replace("K",""))*1000);
                                        console.log(content)
                                        content = content + 1;
                                        content = (content/1000) + 'K';
                                    }else{
                                        content = Number(content) + 1;
                                        if(content>999){
                                            content = (content/1000) + 'K';
                                        }
                                    }

                                    p.innerHTML = content;
                                    document.getElementById('likeNum').style.display = 'inline-block';
                                }

                            })
                            .catch(error => {
                                console.error('Fetch error:', error);
                            });
                    }
                }


                const gameIframe = document.getElementById('customGameIframe');
                const fullscreenBtn = document.getElementById('fullscreen-btn');
                if (isMobile()) {
                    const gameContainer = document.getElementById('customGameIframeContainer');
                    const exitFullscreenBtn = document.getElementById('exit-fullscreen-btn');
                    const domainWatermark = document.getElementById('domain-watermark');
                    const playGameBtn = document.querySelector('.play-game');




                    exitFullscreenBtn.addEventListener('click', () => {
                        exitFullscreen(gameContainer, gameIframe, fullscreenBtn, exitFullscreenBtn, domainWatermark);
                        document.querySelector('.custom-thumbnail-container').style.display = 'block';
                        document.querySelector('.bottomTdkFullscreen').style.display = 'block';
                        document.getElementById('gamePlayerBg').style.display = 'block';
                        document.getElementById('play').style = '';
                        document.getElementById('customGameIframe').style = '';
                    });

                    playGameBtn.addEventListener('click', () => {
                        document.querySelector('.custom-thumbnail-container').style.display = 'none';
                        document.getElementById('gamePlayerBg').style.display = 'none';
                        gameContainer.style.display = 'block';
                        if(document.querySelector('.bottomTdkFullscreen').style.display == 'none'){
                            enterFullscreen(gameContainer, gameIframe, fullscreenBtn, exitFullscreenBtn, domainWatermark);
                        }
                        // gameContainer.classList.remove('hidden');
                        enterFullscreen(gameContainer, gameIframe, fullscreenBtn, exitFullscreenBtn, domainWatermark);
                        document.querySelector('.bottomTdkFullscreen').style.display = 'none';
                        ajaxClick();
                    });


                } else {
                    fullscreenBtn.addEventListener('click', function() {
                        if(document.querySelector('.custom-thumbnail-container').style.display == 'none'){
                            if (gameIframe.requestFullscreen) {
                                gameIframe.requestFullscreen();
                            } else if (gameIframe.mozRequestFullScreen) { // Firefox
                                gameIframe.mozRequestFullScreen();
                            } else if (gameIframe.webkitRequestFullscreen) { // Chrome, Safari and Opera
                                gameIframe.webkitRequestFullscreen();
                            } else if (gameIframe.msRequestFullscreen) { // IE/Edge
                                gameIframe.msRequestFullscreen();
                            }
                        }else{
                            const play = document.getElementById('play');
                            if (play.requestFullscreen) {
                                play.requestFullscreen();
                            } else if (play.mozRequestFullScreen) { // Firefox
                                play.mozRequestFullScreen();
                            } else if (play.webkitRequestFullscreen) { // Chrome, Safari and Opera
                                play.webkitRequestFullscreen();
                            } else if (play.msRequestFullscreen) { // IE/Edge
                                play.msRequestFullscreen();
                            }
                        }
                        ajaxClick();
                    });
                }

                function isMobile() {
                    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
                }

                function isLandscape() {
                    return window.innerWidth > window.innerHeight;
                }

                function handleMobileFullscreen(gameContainer, gameIframe, fullscreenBtn, exitFullscreenBtn, domainWatermark) {
                    // setTimeout(function() {
                    //     domainWatermark.style.display = 'block';
                    // }, 1000);
                    // document.querySelector('header').style.display = 'none';


                    if(document.querySelector('.custom-thumbnail-container').style.display == 'none'){
                        gameContainer.style.height = '100vh';
                        gameContainer.style.width = '100vw';
                        gameContainer.style.position = 'fixed';
                        gameContainer.style.top = '0px';
                        gameContainer.style.left = '0';
                        gameContainer.style.zIndex = '9999';
                        gameContainer.style.backgroundColor = '#000';

                        if (isLandscape()) {
                            gameIframe.style.width = '100vw';
                            gameIframe.style.height = '100vh';
                            gameIframe.style.transform = 'none';
                            gameIframe.style.position = 'static';
                            gameIframe.style.display = 'block';
                            exitFullscreenBtn.style.transform = 'none';
                            // domainWatermark.style.display = 'block';
                        } else {
                            gameIframe.style.width = '100vh';
                            gameIframe.style.height = '100vw';
                            gameIframe.style.transform = 'rotate(90deg)';
                            gameIframe.style.transformOrigin = 'top left';
                            gameIframe.style.position = 'absolute';
                            gameIframe.style.top = '0';
                            gameIframe.style.left = '100%';
                            gameIframe.style.display = 'block';
                            exitFullscreenBtn.style.transform = 'rotate(90deg)';
                            // domainWatermark.style.display = 'none';
                        }

                        // fullscreenBtn.style.display = 'none';
                        exitFullscreenBtn.style.display = 'block';

                        if (screen.orientation && screen.orientation.lock) {
                            screen.orientation.lock('landscape').catch(() => {
                                console.log('Landscape lock not supported');
                            });
                        }
                    }else{
                        const play = document.getElementById('play');
                        play.style.height = '100vh';
                        play.style.width = '100vw';
                        play.style.position = 'fixed';
                        play.style.top = '-25px';
                        play.style.left = '0';
                        play.style.zIndex = '9999';
                        play.style.backgroundColor = '#000';
                    }


                }

                function handleDesktopFullscreen(gameIframe, fullscreenBtn, exitFullscreenBtn) {
                    if (gameIframe.requestFullscreen) {
                        gameIframe.requestFullscreen();
                    } else if (gameIframe.mozRequestFullScreen) {
                        gameIframe.mozRequestFullScreen();
                    } else if (gameIframe.webkitRequestFullscreen) {
                        gameIframe.webkitRequestFullscreen();
                    } else if (gameIframe.msRequestFullscreen) {
                        gameIframe.msRequestFullscreen();
                    }
                    // gameIframe.style.display = 'none';
                    // fullscreenBtn.style.display = 'none';
                    exitFullscreenBtn.style.display = 'block';
                }

                function enterFullscreen(gameContainer, gameIframe, fullscreenBtn, exitFullscreenBtn, domainWatermark) {
                    if (isMobile()) {
                        handleMobileFullscreen(gameContainer, gameIframe, fullscreenBtn, exitFullscreenBtn, domainWatermark);
                    }
                }

                function exitFullscreen(gameContainer, gameIframe, fullscreenBtn, exitFullscreenBtn, domainWatermark) {

                    if (document.exitFullscreen) {
                        document.exitFullscreen();
                    } else if (document.mozCancelFullScreen) {
                        document.mozCancelFullScreen();
                    } else if (document.webkitExitFullscreen) {
                        document.webkitExitFullscreen();
                    } else if (document.msExitFullscreen) {
                        document.msExitFullscreen();
                    }

                    if (isMobile()) {
                        // document.querySelector('header').style.display = 'block';
                        gameContainer.style = '';
                        gameIframe.style.display = 'none';
                        domainWatermark.style.display = 'none';
                        if (screen.orientation && screen.orientation.unlock) {
                            screen.orientation.unlock();
                        }
                    }
                    // gameIframe.style.display = 'none';
                    // fullscreenBtn.style.display = 'block';
                    exitFullscreenBtn.style.display = 'none';
                }

                function handleFullscreenChange(gameContainer, gameIframe, fullscreenBtn, exitFullscreenBtn, domainWatermark) {
                    if (!document.fullscreenElement && !document.webkitFullscreenElement &&
                        !document.mozFullScreenElement && !document.msFullscreenElement) {
                        exitFullscreen(gameContainer, gameIframe, fullscreenBtn, exitFullscreenBtn, domainWatermark);
                    }
                }

                function autoEnterFullscreen(gameContainer, gameIframe, fullscreenBtn, exitFullscreenBtn, domainWatermark) {
                    if (isMobile() && !isLandscape()) {
                        handleMobileFullscreen(gameContainer, gameIframe, fullscreenBtn, exitFullscreenBtn, domainWatermark);
                    }
                }

            </script>
        </div>

        <style>
            .share-encouragement {
                background: linear-gradient(135deg, #f5f6fa 0%, #e5e5e5 100%);
                border-radius: 12px;
                padding: 20px 40px;
                margin-bottom: 20px;
                display: flex;
                align-items: center;
                box-shadow: 0 4px 15px rgba(0,0,0,0.05);
                border: 1px solid rgba(108, 92, 231, 0.15);
            }

            .share-icon {
                background-color: rgba(108, 92, 231, 0.1);
                border-radius: 50%;
                width: 60px;
                height: 60px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 20px;
                flex-shrink: 0;
            }

            .share-icon svg {
                width: 30px;
                height: 30px;
            }

            .share-text h3 {
                color: var(--primary-color);
                font-size: 1.2rem;
                margin-bottom: 8px;
                font-weight: 700;
            }

            .share-text p {
                color: var(--text-light);
                font-size: 0.95rem;
                line-height: 1.5;
                margin: 0;
            }

            @media (max-width: 768px) {
                .share-encouragement {
                    flex-direction: column;
                    text-align: center;
                    padding: 15px;
                }

                .share-icon {
                    margin-right: 0;
                    margin-bottom: 15px;
                }

                .share-text h3 {
                    font-size: 1.1rem;
                }

                .share-text p {
                    font-size: 0.9rem;
                }
            }

            @media (max-width: 480px) {
                .share-icon {
                    width: 50px;
                    height: 50px;
                }

                .share-icon svg {
                    width: 25px;
                    height: 25px;
                }
            }
        </style>
        <div class="share-encouragement">
            <div class="share-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#6c5ce7">
                    <path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92 1.61 0 2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92z"/>
                </svg>
            </div>
            <div class="share-text">
                <h3>Love This Game?</h3>
                <p>Share it with your friends and spread the fun! Your support helps us grow.</p>
            </div>
        </div>
        
<div class="media-links" style="margin-bottom:45px;">
<div class="smi row "><style>/* share */
.smi {
    width: 100%;
    color: #fff;
    text-align: center;
    font-size: 16px;
    margin-bottom: 3px;

    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: calc(var(--bs-gutter-y)* -1);
    margin-right: calc(var(--bs-gutter-x)* -.5);
    margin-left: calc(var(--bs-gutter-x)* -.5);
}

.smi i {
    margin: 0 !important;
}

.smi .email {
    background-color: #5f5180;
}

.smi .facebook {
    background-color: #3061c4;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
}

.smi .linkedin {
    background-color: #0d77b7;
}

.smi .twitter {
    background-color: #111111ee;
}

.smi .whatsapp {
    background-color: rgb(88, 190, 85);
}

.smi .rss {
    background-color: orange;
}

.smi .reddit {
    background-color: rgb(224, 90, 49);
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

.smi a {
    color: #fff;
    font-weight: 500;
}

.smi .share_btn {
    color: #fff;
    font-size: 15px;
    font-weight: 500;
    line-height: 21px;
    width: 20%;
    min-windth:160px;

    margin-top:10px;
    padding: 5px;
    text-decoration: none;
    border: none !important;
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
}

.smi .share_btn_wrap:hover {
    opacity: 0.9;
}

.smi .fav svg {
    height: 15px;
    width: 15px;
    fill: #fff;
    display: inline-block;
    margin-top: -3px;
    margin-right: 6px;
}</style><button class="col fav p-2 share_btn_wrap facebook share_btn" onclick="if (!window.__cfRLUnblockHandlers) return false; sm_share('https://www.facebook.com/sharer/sharer.php?u=https://sprunki.com/sprunki-retake','Facebook','600','300');" aria-label="facebook" title="facebook" data-cf-modified-f92e0af06a98d6925affa0f3-=""><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path d="M279.14 288l14.22-92.66h-88.91v-60.13c0-25.35 12.42-50.06 52.24-50.06h40.42V6.26S260.43 0 225.36 0c-73.22 0-121.08 44.38-121.08 124.72v70.62H22.89V288h81.39v224h100.17V288z"></path></svg><span class="d-none d-lg-inline-block">Facebook</span></button><button class="col fav p-2 share_btn_wrap twitter share_btn" onclick="if (!window.__cfRLUnblockHandlers) return false; sm_share('http://twitter.com/share?text=Sprunki Incredibox&amp;url=https://sprunki.com/sprunki-retake','Twitter','600','300');" aria-label="twitter" title="twitter" data-cf-modified-f92e0af06a98d6925affa0f3-=""><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30"><path fill="white" d="M22.1885 3.75H26.0219L17.6469 13.3229L27.5 26.3469H19.7854L13.7437 18.4469L6.82917 26.3469H2.99375L11.9521 16.1073L2.5 3.75104H10.4104L15.8719 10.9719L22.1885 3.75ZM20.8438 24.0531H22.9677L9.25625 5.92396H6.97708L20.8438 24.0531Z"></path></svg><span class="d-none d-lg-inline-block">Twitter</span></button><button class="col fav p-2 share_btn_wrap linkedin share_btn" onclick="if (!window.__cfRLUnblockHandlers) return false; sm_share('https://www.linkedin.com/sharing/share-offsite/?url=https://sprunki.com/sprunki-retake','Linkedin','600','300');" aria-label="linkedin" title="linkedin" data-cf-modified-f92e0af06a98d6925affa0f3-=""><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M100.28 448H7.4V148.9h92.88zM53.79 108.1C24.09 108.1 0 83.5 0 53.8a53.79 53.79 0 0 1 107.58 0c0 29.7-24.1 54.3-53.79 54.3zM447.9 448h-92.68V302.4c0-34.7-.7-79.2-48.29-79.2-48.29 0-55.69 37.7-55.69 76.7V448h-92.78V148.9h89.08v40.8h1.3c12.4-23.5 42.69-48.3 87.88-48.3 94 0 111.28 61.9 111.28 142.3V448z"></path></svg><span class="d-none d-lg-inline-block">Linkedin</span></button><button class="col fav p-2 share_btn_wrap whatsapp share_btn" onclick="if (!window.__cfRLUnblockHandlers) return false; sm_share('https://api.whatsapp.com/send?text=Sprunki Incredibox https://sprunki.com/sprunki-retake','WhatsApp','700','650');" aria-label="whatsapp" title="whatsapp" data-cf-modified-f92e0af06a98d6925affa0f3-=""><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 224.1-99.6 224.1-222 0-59.3-25.2-115-67.1-157zm-157 341.6c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 359.2l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56.2 81.2 56.1 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7.9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z"></path></svg><span class="d-none d-lg-inline-block">WhatsApp</span></button><button class="col fav p-2 share_btn_wrap reddit share_btn" onclick="if (!window.__cfRLUnblockHandlers) return false; sm_share('https://www.reddit.com/login/?dest=https%3A%2F%2Fwww.reddit.com%2Fsubmit%3Ftitle%3DSprunki Incredibox%26url%3Dhttps://sprunki.com/sprunki-retake','Reddit','600','436');" aria-label="reddit" title="reddit" data-cf-modified-f92e0af06a98d6925affa0f3-=""><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30"><path d="M20.3032 16.2506C20.3032 16.659 20.141 17.0507 19.8522 17.3395C19.5634 17.6283 19.1717 17.7905 18.7633 17.7905C18.3548 17.7905 17.9631 17.6283 17.6743 17.3395C17.3855 17.0507 17.2233 16.659 17.2233 16.2506C17.2233 15.8422 17.3855 15.4505 17.6743 15.1617C17.9631 14.8729 18.3548 14.7106 18.7633 14.7106C19.1717 14.7106 19.5634 14.8729 19.8522 15.1617C20.141 15.4505 20.3032 15.8422 20.3032 16.2506ZM11.2305 17.79C11.6388 17.79 12.0303 17.6278 12.319 17.3391C12.6077 17.0504 12.7699 16.6589 12.7699 16.2506C12.7699 15.8423 12.6077 15.4507 12.319 15.162C12.0303 14.8733 11.6388 14.7111 11.2305 14.7111C10.8222 14.7111 10.4306 14.8733 10.1419 15.162C9.85323 15.4507 9.69104 15.8423 9.69104 16.2506C9.69104 16.6589 9.85323 17.0504 10.1419 17.3391C10.4306 17.6278 10.8222 17.79 11.2305 17.79ZM11.6486 19.5795C11.4699 19.4743 11.2573 19.4429 11.0558 19.4918C10.8543 19.5407 10.6798 19.6661 10.5692 19.8414C10.4586 20.0168 10.4205 20.2283 10.4631 20.4312C10.5057 20.6341 10.6257 20.8124 10.7975 20.9285L11.1645 21.1604C12.3114 21.8838 13.6396 22.2676 14.9955 22.2676C16.3515 22.2676 17.6797 21.8838 18.8266 21.1604L19.1936 20.9295C19.2823 20.8736 19.3592 20.8008 19.4197 20.7152C19.4803 20.6297 19.5234 20.533 19.5467 20.4307C19.5699 20.3285 19.5727 20.2227 19.555 20.1193C19.5374 20.016 19.4995 19.9171 19.4436 19.8284C19.3877 19.7397 19.3149 19.6629 19.2293 19.6023C19.1438 19.5417 19.0471 19.4986 18.9448 19.4754C18.8426 19.4521 18.7368 19.4493 18.6334 19.467C18.5301 19.4846 18.4312 19.5225 18.3425 19.5784L17.9755 19.8103C17.0834 20.373 16.0503 20.6717 14.9955 20.6717C13.9408 20.6717 12.9077 20.373 12.0156 19.8103L11.6486 19.5795Z" fill="white"></path><path d="M23.4684 3.75C22.5396 3.75 21.7279 4.2596 21.3002 5.01389L17.4702 4.22768C17.2897 4.19071 17.102 4.21735 16.9389 4.30306C16.7758 4.38877 16.6474 4.52829 16.5755 4.69792C16.1382 5.72562 15.4829 7.28421 14.935 8.61831C14.7414 9.09067 14.5605 9.53644 14.4084 9.92156C12.2306 10.0088 10.2135 10.578 8.56342 11.5004C8.29782 11.055 7.92984 10.6793 7.49003 10.4045C7.05021 10.1298 6.55122 9.96386 6.03445 9.92052C5.51768 9.87719 4.99802 9.95769 4.51858 10.1553C4.03914 10.353 3.61375 10.6621 3.27767 11.0571C2.94159 11.452 2.70453 11.9214 2.58614 12.4263C2.46775 12.9312 2.47145 13.457 2.59692 13.9602C2.7224 14.4634 2.96604 14.9294 3.30764 15.3196C3.64923 15.7098 4.07893 16.0129 4.5611 16.2038C4.42067 16.7144 4.34726 17.2432 4.34726 17.79C4.34726 20.0688 5.63349 22.0583 7.5655 23.4467C9.49963 24.8371 12.1295 25.6712 14.9999 25.6712C17.8692 25.6712 20.5001 24.8371 22.4343 23.4477C24.3663 22.0583 25.6525 20.0688 25.6525 17.79C25.6525 17.2432 25.578 16.7134 25.4397 16.2048C25.9217 16.0138 26.3513 15.7107 26.6927 15.3205C27.0342 14.9304 27.2777 14.4644 27.4031 13.9613C27.5286 13.4583 27.5322 12.9325 27.4139 12.4277C27.2955 11.923 27.0585 11.4537 26.7226 11.0587C26.3866 10.6638 25.9613 10.3547 25.482 10.157C25.0027 9.95928 24.4832 9.87866 23.9665 9.92183C23.4499 9.96501 22.9509 10.1307 22.5111 10.4052C22.0712 10.6798 21.7032 11.0552 21.4374 11.5004C19.9193 10.6524 18.0915 10.1024 16.1148 9.95135C16.2085 9.72155 16.3074 9.47686 16.4127 9.22366C16.8574 8.13744 17.3766 6.89908 17.7894 5.92244L21.0002 6.58205C21.0653 7.05616 21.2656 7.50149 21.577 7.86485C21.8885 8.22821 22.2979 8.49424 22.7565 8.63115C23.2151 8.76806 23.7034 8.77008 24.163 8.63695C24.6227 8.50382 25.0343 8.24118 25.3488 7.8804C25.6632 7.51962 25.8671 7.07595 25.9362 6.60239C26.0053 6.12883 25.9365 5.6454 25.7382 5.20985C25.54 4.77429 25.2205 4.40503 24.818 4.14616C24.4155 3.88729 23.9469 3.74976 23.4684 3.75ZM22.5726 6.2416C22.5726 6.00389 22.667 5.7759 22.8351 5.60781C23.0032 5.43972 23.2312 5.34529 23.4689 5.34529C23.7066 5.34529 23.9346 5.43972 24.1027 5.60781C24.2708 5.7759 24.3652 6.00389 24.3652 6.2416C24.3652 6.47932 24.2708 6.70731 24.1027 6.8754C23.9346 7.04349 23.7066 7.13792 23.4689 7.13792C23.2312 7.13792 23.0032 7.04349 22.8351 6.8754C22.667 6.70731 22.5726 6.47932 22.5726 6.2416ZM14.9999 11.5057C15.1637 11.5057 15.3265 11.5089 15.4893 11.5153C15.4876 11.5489 15.4833 11.5823 15.4765 11.6153L15.4744 11.6206L15.4776 11.6099L15.5084 11.5163C17.8745 11.6078 19.9778 12.3355 21.5023 13.4313C23.1375 14.6058 24.0567 16.158 24.0567 17.7911C24.0567 19.4252 23.1364 20.9774 21.5034 22.153C19.8693 23.3254 17.5734 24.0765 14.9999 24.0765C12.4264 24.0765 10.1305 23.3254 8.49746 22.1519C6.86227 20.9774 5.94308 19.4252 5.94308 17.7911C5.94308 16.158 6.86227 14.6058 8.49639 13.4313C10.1316 12.2546 12.4274 11.5036 14.9999 11.5036V11.5057ZM4.09512 13.1706C4.09521 12.7993 4.21938 12.4386 4.4479 12.1459C4.67642 11.8531 4.9962 11.6451 5.35644 11.5549C5.71669 11.4647 6.09674 11.4974 6.43626 11.6479C6.77577 11.7984 7.05527 12.058 7.23038 12.3855C6.38034 13.0557 5.67392 13.8462 5.17283 14.7303C4.85604 14.6106 4.58323 14.3972 4.39071 14.1186C4.19818 13.84 4.09508 13.5093 4.09512 13.1706ZM24.8269 14.7303C24.3258 13.8473 23.6194 13.0547 22.7694 12.3855C22.8965 12.1481 23.0792 11.9451 23.3018 11.7938C23.5245 11.6424 23.7806 11.5473 24.0481 11.5166C24.3156 11.4859 24.5865 11.5204 24.8377 11.6173C25.0889 11.7142 25.3129 11.8705 25.4905 12.0729C25.6681 12.2753 25.794 12.5177 25.8574 12.7793C25.9208 13.041 25.9199 13.3141 25.8546 13.5754C25.7894 13.8366 25.6618 14.0781 25.4829 14.2793C25.3039 14.4804 25.0788 14.6352 24.8269 14.7303Z" fill="white"></path></svg><span class="d-none d-lg-inline-block"> Reddit</span></button>
    <script type="f92e0af06a98d6925affa0f3-text/javascript">// Social media share
    function sm_share(url, title, w, h) {
        "use strict";

        var dualScreenLeft =
            window.screenLeft != undefined ? window.screenLeft : screen.left;
        var dualScreenTop =
            window.screenTop != undefined ? window.screenTop : screen.top;

        var width = window.innerWidth ?
            window.innerWidth :
            document.documentElement.clientWidth ?
                document.documentElement.clientWidth :
                screen.width;
        var height = window.innerHeight ?
            window.innerHeight :
            document.documentElement.clientHeight ?
                document.documentElement.clientHeight :
                screen.height;

        var left = width / 2 - w / 2 + dualScreenLeft;
        var top = height / 2 - h / 2 + dualScreenTop;
        var newWindow = window.open(
            url,
            title,
            "scrollbars=yes, width=" +
            w +
            ", height=" +
            h +
            ", top=" +
            top +
            ", left=" +
            left
        );

        if (window.focus) {
            newWindow.focus();
        }
    }</script>
</div>
</div>

        <div class="ad-container" style="min-height:305px;">
            <!--&lt;!&ndash; sprunki.com.head1 &ndash;&gt;-->
            <ins class="adsbygoogle"
                 style="display:block"
                 data-ad-client="ca-pub-2680823943926888"
                 data-ad-slot="6372422913"
                 data-ad-format="auto"
                 data-full-width-responsive="true"></ins>

            <script type="f92e0af06a98d6925affa0f3-text/javascript">
                (adsbygoogle = window.adsbygoogle || []).push({});
            </script>
        </div>
        <!--<div class="ad-banner">-->
        <!--    Advertisement - 728x90-->
        <!--</div>-->
        <div class="section-title">
            <i class="fas fa-gamepad"></i> New Games        </div>
        <div class="game-grid">
                                                <a href="/sprunki-wenda-treatment-pyramixed" title="Sprunki Wenda Treatment Pyramixed">
                                                <div class="game-card">
                            <img src="https://cdn.sprunki.com/img/sprunki-wenda-treatment-pyramixed.webp" alt="Sprunki Wenda Treatment Pyramixed">
                            <h3>Sprunki Wenda Treatment Pyramixed</h3>
                        </div>
                    </a>
                                                <a href="/sprunki-phase-24" title="Sprunki Phase 24">
                                                <div class="game-card">
                            <img src="https://cdn.sprunki.com/img/sprunki-phase-24.webp" alt="Sprunki Phase 24">
                            <h3>Sprunki Phase 24</h3>
                        </div>
                    </a>
                                                <a href="/sprunki-phase-9-definitive-new" title="Sprunki Phase 9 Definitive New">
                                                <div class="game-card">
                            <img src="https://cdn.sprunki.com/img/sprunki-phase-9-definitive.webp" alt="Sprunki Phase 9 Definitive New">
                            <h3>Sprunki Phase 9 Definitive New</h3>
                        </div>
                    </a>

                    </div>
    </div>

    <!-- Right Sidebar -->
    <div class="right-sidebar">
        <!--<div class="ad-banner">-->
        <!--    Advertisement - 300x250-->
        <!--</div>-->
        <div class="ad-container" style="min-height: 280px;">
            <!--&lt;!&ndash; sprunki.com.head1 &ndash;&gt;-->
            <ins class="adsbygoogle"
                 style="display:block"
                 data-ad-client="ca-pub-2680823943926888"
                 data-ad-slot="6372422913"
                 data-ad-format="auto"
                 data-full-width-responsive="true"></ins>

            <script type="f92e0af06a98d6925affa0f3-text/javascript">
                (adsbygoogle = window.adsbygoogle || []).push({});
            </script>
        </div>
        <div class="section-title">
            <i class="fas fa-fire"></i> Trending Games        </div>
        <div class="game-grid">
                                                <a href="/sprunki-inverted" title="Sprunki Inverted">
                                                <div class="game-card">
                            <img src="https://cdn.sprunki.com/img/Sprunki%20Inverted.webp" alt="Sprunki Inverted">
                            <h3>Sprunki Inverted</h3>
                        </div>
                    </a>


        </div>
    </div>
</div>
<!-- 中部2 - 分类 -->
<div class="categories content-area-2">
    <div class="container">
        <div class="category-list">
            <a href="phase" class="category-item"><i class="fas fa-running"></i> PHASE</a>
            <a href="horror" class="category-item"><i class="fas fa-map-marked-alt"></i> HORROR</a>
            <a href="trending" class="category-item"><i class="fas fa-puzzle-piece"></i> TRENDING</a>
            <a href="mod" class="category-item"><i class="fas fa-futbol"></i> MOD</a>
            <a href="original" class="category-item"><i class="fas fa-car"></i> ORIGINAL</a>
        </div>
    </div>
</div>
<!-- 中部3 - 关于和评分 -->
<div class="container content-area-2">
    <div class="about-section">
        <div class="about-left">
            <style>
                .about-section {
                    text-align: left;
                }
            </style>
            <section id="features" class="section">
<h2>Sprunki Retake: Unleash Your Musical Creativity with Exciting New Features!</h2>
<h3>Introduction to Sprunki Retake</h3>
<p>Sprunki Retake is an innovative music-mixing game that takes the beloved Incredibox-inspired gameplay to new heights. This mod introduces expressive hand animations for each character, adding a vibrant layer of personality and interactivity. Players can witness their favorite Sprunki characters come to life as they perform rhythmic beats, clapping, snapping, and pointing in sync with the music. The unique hand movements not only enhance the visual experience but also create an immersive environment where players can engage with the music on a whole new level. The mod transforms the classic gameplay into a lively musical adventure, inviting players to explore their creativity while crafting unique soundscapes.</p>
<h3>Why Do Players Love Playing Sprunki Retake?</h3>
<p>Players are captivated by Sprunki Retake for its engaging visuals and interactive gameplay. The addition of animated hands allows for a more dynamic connection with the characters, making each performance feel like a live show. The unique gestures and movements of the characters add humor and charm, enriching the overall experience. Players appreciate how the mod encourages creativity, allowing them to mix and match sounds while watching their characters showcase expressive hand gestures. This blend of music creation and visual storytelling makes Sprunki Retake a game that resonates with fans of rhythm games and music enthusiasts alike.</p>
<p><strong>Key Features of Sprunki Retake</strong></p>
<ul>
    <li>
<strong>Expressive Hand Animations:</strong> Each character features detailed hand movements that sync perfectly with their musical contributions.</li>
    <li>
<strong>Interactive Gameplay:</strong> Players can create and modify tracks while enjoying the lively animations of their characters.</li>
    <li>
<strong>Unique Character Personalities:</strong> Every character brings their own flair to the mix with distinct gestures, enhancing the fun and engagement.</li>
    <li>
<strong>Creative Sound Combinations:</strong> Experiment with various sounds to discover new rhythms and create captivating musical performances.</li>
</ul>
<h3>Benefits of Playing Sprunki Retake</h3>
<p>Playing Sprunki Retake offers an engaging experience where creativity flourishes. The combination of music and animated visuals allows players to express themselves while enjoying a vibrant soundscape. The game fosters a sense of community among players, encouraging collaboration and sharing of unique tracks.</p>
<h3>Gameplay Guide for Sprunki Retake</h3>
<p><strong>Important Notice: Loading Time</strong></p>
<p>Please be patient as the game loads for 30-45 seconds. You will see a loading animation as the game prepares all the exciting music elements. Rest assured, the wait will be well worth it!</p>
<p><strong>Step-by-Step Guide:</strong></p>
<ol>
    <li>Use the mouse to click on characters from the selection menu and drag them onto the stage.</li>
    <li>Arrange the characters to create your desired sound combinations.</li>
    <li>Click on a character on the stage to remove it if needed.</li>
    <li>Watch as your characters perform their sounds with lively hand gestures.</li>
</ol>
<p><strong>Tips to Play Sprunki Retake:</strong></p>
<ul>
    <li>Experiment with different sound combinations to discover unique rhythms.</li>
    <li>Pay attention to the hand gestures as they can enhance the overall vibe of your track.</li>
    <li>Share your creations with friends to showcase your musical talent!</li>
</ul>
<h3>FAQs about Sprunki Retake</h3>
<p><strong>Q: What types of sounds can I use in the game?<br>A: </strong>You can use a variety of beats, effects, and vocals to create your mixes.</p>
<p><strong>Q: Are there any limitations on character selection?<br>A: </strong>Each character is available for selection, allowing for diverse musical combinations.</p>
<p><strong>Q: Can I customize the gestures of the characters?<br>A: </strong>The gestures are predefined, but each character has unique animations that match their sounds.</p>
<p><strong>Q: Is there a way to save my mixes?<br>A: </strong>While saving mixes isn't available, you can share your performances with others.</p>
<p><strong>Q: How do I know which characters work well together?<br>A: </strong>Experimentation is key! Try different combinations to see which characters complement each other.</p>
<p><strong>Q: Are there any special effects triggered by certain combinations?<br>A: </strong>Yes, some sound combinations will trigger unique gestures and animations.</p>
<p><strong>Q: Can I play with friends?<br>A: </strong>While the game is single-player, you can share your creations and collaborate with friends outside the game.</p>
<p><strong>Q: What devices can I play this game on?<br>A: </strong>Sprunki Retake is accessible on various devices without the need for downloads.</p>
<h3>Final Thoughts</h3>
<p>Sprunki Retake is a free-to-play game that invites you to dive into a world of musical creativity. With its engaging visuals and interactive gameplay, it's the perfect platform for music lovers to express their artistic flair. Don't miss out on this unique experience—click to play now and discover the joy of creating music in a whole new way! If you're looking for more musical fun, explore other Sprunki games like Sprunki SprunkTune and Sprunki Splunkeh!</p>
</section>        </div>
        <div class="about-right">
        
    <div class="comments-section">
        <h3>Discuss sprunki</h3>
        <div id="tcomment"></div>
        <script src="https://cdn.sprunki.com/twikoo.all.min.js" type="f92e0af06a98d6925affa0f3-text/javascript"></script>
        <script type="f92e0af06a98d6925affa0f3-text/javascript">
            window.addEventListener('load', () => {
                twikoo.init({
                    envId: 'https://user.sprunki.com/', // Twikoo 环境地址
                    el: '#tcomment',
                    region: '',
                    path: location.pathname,
                    lang: 'en-US',
                })
                
                const MAX_ATTEMPTS = 30;  // 30 attempts (30 * 200ms = 6s total)
                const RETRY_DELAY = 2000;  // Check every 200ms
                const TARGET_PLACEHOLDER = "Join the conversation...\nNo login required, just type and send...";
                const NEW_PLACEHOLDER = "Thank you for your comments! Share your favorite Sprunki Mods and Characters!";
        
                let attempts = 0;
        
                const tryUpdatePlaceholder = function() {
                    attempts++;
        
                    const textarea = document.querySelector('#twikoo textarea.el-textarea__inner');
        
                    // If found and has original placeholder
                    if (textarea && textarea.placeholder !== NEW_PLACEHOLDER) {
                        textarea.placeholder = NEW_PLACEHOLDER;
                        textarea.style.fontStyle = "italic";
                        textarea.style.color = "#6c5ce7";
                        return; // Success - exit
                    }
        
                    // If not found but still within attempt limit
                    if (attempts < MAX_ATTEMPTS) {
                        setTimeout(tryUpdatePlaceholder, RETRY_DELAY);
                    }
                };
        
                // Start trying
                setTimeout(tryUpdatePlaceholder, RETRY_DELAY);
            })
        </script>
    </div>


        </div>
    </div>
</div>

<!-- Footer -->
<footer>

    <div class="footer-top">
        <div class="logo"><img width="auto" height="37" src="/static/mb9/picture/logo.png" class="logo" alt="Sprunki"></div>
        <div class="footer-links">
            <a href="/contact" class="contact">CONTACT</a>
            <a href="/terms" class="cgu">TERMS OF USE</a>
            <a href="/about" class="cgu">ABOUT</a>
            <a href="/privacy" class="privacy">PRIVACY</a>
            <a href="/blog" title="BLOGS">BLOGS</a>
        </div>

    </div>
    <div class="footer-disclaimer" style="margin: 0 auto;width: 100%;text-align: center;font-size:13px;">
        <p>Disclaimer: Sprunki.com is an independent platform providing Sprunki mods and is not affiliated with the original game developers.  </p>
        <p>PLEASE NOTE: We do not own or endorse any third-party services or products claiming association with Sprunki.com. Any such claims are fraudulent.</p>
    </div>
    
</footer>

<script src="/cdn-cgi/scripts/7d0fa10a/cloudflare-static/rocket-loader.min.js" data-cf-settings="f92e0af06a98d6925affa0f3-|49" defer></script><script defer src="https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015" integrity="sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==" data-cf-beacon='{"version":"2024.11.0","token":"0d2ce1b71f4d4070affe1e6dcb7c59d3","server_timing":{"name":{"cfCacheStatus":true,"cfEdge":true,"cfExtPri":true,"cfL4":true,"cfOrigin":true,"cfSpeedBrain":true},"location_startswith":null}}' crossorigin="anonymous"></script>
</body>
</html>