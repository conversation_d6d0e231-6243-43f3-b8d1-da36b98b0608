'use client';
import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';

// 添加全局错误处理
if (typeof window !== 'undefined') {
  window.addEventListener('error', function(event) {
    console.log('捕获到JS错误:', event.message);
    // 尝试恢复React状态管理
    if (event.message.includes('Unexpected end of input')) {
      console.log('尝试恢复损坏的JS');
    }
  });
}

export default function Home() {
  const [isGameLoaded, setIsGameLoaded] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [likeCount, setLikeCount] = useState('6.514K');

  const startGame = () => {
    setIsGameLoaded(true);
  };

  const toggleFullscreen = () => {
    const gameContainer = document.getElementById('game-container');
    if (!document.fullscreenElement) {
      gameContainer?.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  const handleLike = () => {
    // 模拟点赞功能
    const currentCount = parseFloat(likeCount.replace('K', '')) * 1000;
    const newCount = currentCount + 1;
    setLikeCount((newCount / 1000).toFixed(3) + 'K');
  };

  return (
    <main className="min-h-screen bg-gradient-to-b from-gray-100 to-gray-200">
      {/* Main Content Area */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="flex flex-wrap gap-6">

          {/* Left Sidebar */}
          <aside className="hidden lg:block w-36 flex-shrink-0">
            <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
              <h3 className="text-sm font-bold text-gray-800 mb-4 flex items-center">
                <span className="text-blue-500 mr-2">🎮</span>
                Popular Games
              </h3>
              <div className="space-y-3">
                <Link href="/sprunki" className="block">
                  <div className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                    <Image
                      src="https://cdn.sprunki.com/img/sprunky.webp"
                      alt="Sprunki"
                      width={120}
                      height={90}
                      className="w-full h-20 object-cover"
                    />
                    <div className="p-2">
                      <h4 className="text-xs font-medium text-gray-800 text-center">Sprunki</h4>
                    </div>
                  </div>
                </Link>
              </div>
            </div>
          </aside>

          {/* Main Content */}
          <div className="flex-1 min-w-0">
            {/* Featured Game Container */}
            <div className="bg-white rounded-3xl shadow-lg overflow-hidden mb-6 relative">
              <div
                className="relative w-full h-[650px] bg-cover bg-center bg-no-repeat"
                style={{
                  backgroundImage: "url('/themes/game9mb/public/image/iframe.png')"
                }}
              >
                {!isGameLoaded ? (
                  // Game Thumbnail and Play Button
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center text-white">
                      <div className="mb-8">
                        <Image
                          src="https://cdn.sprunki.com/img/incredibox-sprunki.webp"
                          alt="Sprunki Retake"
                          width={195}
                          height={195}
                          className="rounded-full mx-auto mb-6"
                        />
                      </div>
                      <div className="mb-8">
                        <h1 className="text-5xl font-bold mb-4">Sprunki Retake</h1>
                        <button
                          onClick={startGame}
                          className="bg-white text-green-600 px-8 py-4 rounded-full text-2xl font-bold hover:bg-gray-100 transition-colors uppercase shadow-lg"
                        >
                          Play Now
                        </button>
                      </div>
                    </div>
                  </div>
                ) : (
                  // Game iframe
                  <iframe
                    src="https://game.sprunki.com/sprunki-retake/index.html"
                    className="w-full h-full border-0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                    title="Sprunki Retake"
                  />
                )}
              </div>

              {/* Bottom Game Info Bar */}
              <div className="bg-white h-16 flex items-center justify-between px-6">
                <div className="flex items-center">
                  <Image
                    src="https://cdn.sprunki.com/img/sprunki-retake.webp"
                    alt="Sprunki Retake"
                    width={54}
                    height={54}
                    className="rounded mr-4"
                  />
                  <div>
                    <h2 className="font-bold text-gray-800 uppercase">Sprunki Retake</h2>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <button
                    onClick={handleLike}
                    className="flex items-center space-x-2 text-gray-600 hover:text-blue-500 transition-colors"
                  >
                    <span className="text-2xl">👍</span>
                    <span className="font-medium">{likeCount}</span>
                  </button>
                  <button
                    onClick={toggleFullscreen}
                    className="bg-gray-800 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors flex items-center space-x-2"
                  >
                    <span>⛶</span>
                    <span className="hidden sm:inline">Fullscreen</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Right Sidebar */}
          <aside className="w-full lg:w-80 flex-shrink-0">
            {/* Ad Container */}
            <div className="bg-gray-100 rounded-lg p-4 mb-6 min-h-[600px] flex items-center justify-center">
              <div className="text-gray-500 text-center">
                <div className="text-4xl mb-2">📢</div>
                <p>Advertisement Space</p>
              </div>
            </div>

            {/* Popular Games Grid */}
            <div className="bg-white rounded-lg shadow-sm p-4">
              <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center">
                <span className="text-blue-500 mr-2">🎮</span>
                More Games
              </h3>
              <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-2 gap-3">
                {[
                  { title: "Sprunki Wenda Treatment 3.0", image: "https://image.free-gamesonline.com/sprunki-wenda-treatment-3-0.jpeg", href: "https://wenda.free-gamesonline.com" },
                  { title: "Sprunki Treatment Brud", image: "https://image.free-gamesonline.com/sprunki-treatment-brud-screenshot-1.jpg", href: "/sprunki-treatment-brud" },
                  { title: "Sprunki Wenda Treatment", image: "https://image.free-gamesonline.com/sprunki-wenda-treatment-screenshot-1.jpg", href: "/sprunki-wenda-treatment" },
                  { title: "Sprunki Sky Treatment", image: "https://image.free-gamesonline.com/sprunki-sky-treatment.jpeg", href: "/sprunki-sky-treatment" },
                  { title: "Sprunki Phase 3", image: "https://cdn.sprunki.com/img/sprunki-phase-3.webp", href: "/sprunki-phase-3" },
                  { title: "Sprunki Phase 4", image: "https://cdn.sprunki.com/img/sprunki-phase-4.webp", href: "/sprunki-phase-4" },
                ].map((game, index) => (
                  <Link href={game.href} key={index} className="block">
                    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-all transform hover:scale-105">
                      <div className="aspect-square relative">
                        <Image
                          src={game.image}
                          alt={game.title}
                          width={150}
                          height={150}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="p-2">
                        <h4 className="text-xs font-medium text-gray-800 text-center hover:text-blue-600 transition-colors line-clamp-2">
                          {game.title}
                        </h4>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
              <button className="w-full mt-4 bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors font-medium">
                Load More Games
              </button>
            </div>
          </aside>
        </div>
      </div>

      {/* Categories Section */}
      <div className="bg-white py-8">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex flex-wrap justify-center gap-4">
            {[
              { name: "Action Games", color: "bg-red-500", icon: "⚔️" },
              { name: "Puzzle Games", color: "bg-blue-500", icon: "🧩" },
              { name: "Adventure Games", color: "bg-green-500", icon: "🗺️" },
              { name: "Racing Games", color: "bg-yellow-500", icon: "🏎️" },
              { name: "Sports Games", color: "bg-purple-500", icon: "⚽" },
            ].map((category, index) => (
              <div
                key={index}
                className={`${category.color} text-white px-6 py-3 rounded-full flex items-center space-x-2 hover:opacity-90 transition-opacity cursor-pointer`}
              >
                <span className="text-lg">{category.icon}</span>
                <span className="font-medium text-sm uppercase">{category.name}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* About Section */}
      <div className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex flex-col lg:flex-row gap-12 items-center">
            {/* Left Side - Game Info */}
            <div className="lg:w-1/2 bg-white rounded-lg shadow-lg p-8">
              <h2 className="text-3xl font-bold text-blue-600 mb-6">Sprunki Retake</h2>
              <p className="text-gray-700 mb-6">
                Unleash your creativity with Sprunki Retake! Enjoy expressive hand animations and engaging music mixing in this free, no-download game. Create your unique tracks!
              </p>

              {/* Rating Section */}
              <div className="mb-6">
                <div className="flex items-center mb-3">
                  <div className="text-yellow-400 text-xl mr-2">★★★★★</div>
                  <span className="text-gray-600">4.8/5</span>
                </div>

                {/* Rating Bars */}
                <div className="space-y-2">
                  {[
                    { stars: 5, percentage: 85 },
                    { stars: 4, percentage: 12 },
                    { stars: 3, percentage: 2 },
                    { stars: 2, percentage: 1 },
                    { stars: 1, percentage: 0 },
                  ].map((rating, index) => (
                    <div key={index} className="flex items-center text-sm">
                      <span className="w-8 text-gray-600">{rating.stars}★</span>
                      <div className="flex-1 bg-gray-200 rounded-full h-2 mx-3">
                        <div
                          className="bg-yellow-400 h-2 rounded-full"
                          style={{ width: `${rating.percentage}%` }}
                        ></div>
                      </div>
                      <span className="w-8 text-gray-600 text-right">{rating.percentage}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Right Side - Features */}
            <div className="lg:w-1/2 bg-white rounded-lg shadow-lg p-8">
              <h3 className="text-2xl font-bold text-gray-800 mb-6">Why Play Free Games Online?</h3>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="bg-blue-100 rounded-full p-2 flex-shrink-0">
                    <span className="text-blue-600">⚡</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">Instant Play</h4>
                    <p className="text-gray-600 text-sm">No downloads or installations required. Play immediately in your browser.</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-green-100 rounded-full p-2 flex-shrink-0">
                    <span className="text-green-600">💰</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">100% Free</h4>
                    <p className="text-gray-600 text-sm">Enjoy unlimited gaming without any costs or hidden fees.</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-purple-100 rounded-full p-2 flex-shrink-0">
                    <span className="text-purple-600">🛡️</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">Safe & Secure</h4>
                    <p className="text-gray-600 text-sm">Family-friendly content with no personal information required.</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-orange-100 rounded-full p-2 flex-shrink-0">
                    <span className="text-orange-600">📱</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">Cross-Platform</h4>
                    <p className="text-gray-600 text-sm">Play on any device - desktop, tablet, or mobile phone.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      </div>

      {/* Footer Content */}
      <div className="bg-gray-800 text-white py-12">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">

            {/* About Column */}
            <div>
              <h3 className="text-xl font-bold mb-4">About Free Games Online</h3>
              <p className="text-gray-300 text-sm leading-relaxed">
                Discover the best collection of free online games! Play instantly without downloads.
                From action-packed adventures to brain-teasing puzzles, we have something for everyone.
                All games are browser-based and completely free to play.
              </p>
            </div>

            {/* Quick Links */}
            <div>
              <h3 className="text-xl font-bold mb-4">Quick Links</h3>
              <ul className="space-y-2 text-sm">
                <li><Link href="/games" className="text-gray-300 hover:text-white transition-colors">All Games</Link></li>
                <li><Link href="/categories" className="text-gray-300 hover:text-white transition-colors">Categories</Link></li>
                <li><Link href="/new-games" className="text-gray-300 hover:text-white transition-colors">New Games</Link></li>
                <li><Link href="/popular" className="text-gray-300 hover:text-white transition-colors">Popular Games</Link></li>
                <li><Link href="/about" className="text-gray-300 hover:text-white transition-colors">About Us</Link></li>
              </ul>
            </div>

            {/* Contact Info */}
            <div>
              <h3 className="text-xl font-bold mb-4">Connect With Us</h3>
              <div className="space-y-2 text-sm text-gray-300">
                <p>Email: <EMAIL></p>
                <p>Follow us for updates and new games!</p>
                <div className="flex space-x-4 mt-4">
                  <a href="#" className="text-gray-300 hover:text-white transition-colors">Facebook</a>
                  <a href="#" className="text-gray-300 hover:text-white transition-colors">Twitter</a>
                  <a href="#" className="text-gray-300 hover:text-white transition-colors">YouTube</a>
                </div>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-700 mt-8 pt-8 text-center">
            <p className="text-gray-400 text-sm">
              © 2024 Free Games Online. All rights reserved. |
              <Link href="/privacy" className="hover:text-white ml-1">Privacy Policy</Link> |
              <Link href="/terms" className="hover:text-white ml-1">Terms of Service</Link>
            </p>
          </div>
        </div>
    </main>
  );
}
